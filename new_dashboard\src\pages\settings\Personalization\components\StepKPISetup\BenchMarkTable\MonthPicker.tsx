"use client";
import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";

interface MonthPickerProps {
  value: string;
  onChange: (val: string) => void;
}

export default function MonthPicker({ value, onChange }: MonthPickerProps) {
  const [open, setOpen] = React.useState(false);
  const [date, setDate] = React.useState<Date | undefined>(
    value ? new Date(value) : undefined
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-full h-8 text-sm justify-start text-left font-normal"
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "MMM yyyy") : "Select month"}
        </Button>
      </PopoverTrigger>

  
      <PopoverContent
        side="bottom"
        align="start"
        className="z-[9999] w-auto p-0 bg-white dark:bg-neutral-900 shadow-lg border border-gray-200 dark:border-neutral-800"
      >
        <Calendar
          mode="single"
          selected={date}
          onSelect={(newDate: Date | undefined) => {
            setDate(newDate);
            if (newDate) {
              onChange(format(newDate, "yyyy-MM"));
              setOpen(false); // closes after selection
            }
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
